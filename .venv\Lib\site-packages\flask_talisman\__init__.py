# Copyright 2015 Google Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from .talisman import (
    ALLOW_FROM, DEFAULT_CSP_POLICY, DEFAULT_DOCUMENT_POLICY,
    DEFAULT_FEATURE_POLICY, DEFAULT_PERMISSIONS_POLICY, DENY,
    GOO<PERSON><PERSON>_CSP_POLICY, NONCE_LENGTH, SAMEORIGIN, Talisman)

__all__ = (
    'ALLOW_FROM',
    'DEFAULT_CSP_POLICY',
    'DEFAULT_DOCUMENT_POLICY',
    'DEFAULT_FEATURE_POLICY',
    'DEFAULT_PERMISSIONS_POLICY',
    'DENY',
    'GOOGLE_CSP_POLICY',
    'NONCE_LENGTH',
    '<PERSON>MEORIGIN',
    'Talisman',
)
